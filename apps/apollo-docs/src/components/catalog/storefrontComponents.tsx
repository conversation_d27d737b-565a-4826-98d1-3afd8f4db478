import {
  ProductBadge,
  ProductCard,
  ProductCardContent,
  ProductPrice,
  CategoryMenuItem,
  TabWithImage,
} from "@apollo/storefront"

const storefrontComponents = [
  {
    title: "Product Card",
    href: "@apollo/storefront/ProductCard",
    description: "Complete product display with image, title, and pricing",
    keywords: ["product", "card", "display", "image", "title", "pricing"],
    component: (
      <ProductCard
        imageSrc="https://picsum.photos/200/300?random=1"
        title="Premium Wireless Headphones"
        caption="High-quality sound with noise cancellation"
        price={{
          price: "299",
          currency: "$",
          discountPrice: "399",
        }}
      />
    ),
  },
  {
    title: "Product Card Content",
    href: "@apollo/storefront/ProductCardContent",
    description: "Flexible content area for product information",
    keywords: ["product", "card", "content", "information", "flexible"],
    component: (
      <div
        style={{
          padding: "16px",
          border: "1px solid #e0e0e0",
          borderRadius: "8px",
          maxWidth: "200px",
        }}
      >
        <ProductCardContent
          title="Smart Watch Series 5"
          caption="Track your fitness and stay connected"
          price={{
            price: "199",
            currency: "$",
            discountPrice: "299",
          }}
        />
      </div>
    ),
  },
  {
    title: "Product Price",
    href: "@apollo/storefront/ProductPrice",
    description: "Pricing display with discount and currency support",
    keywords: ["price", "pricing", "discount", "currency", "money"],
    component: (
      <div style={{ padding: "16px" }}>
        <ProductPrice price="99" currency="$" discountPrice="149" unit="each" />
      </div>
    ),
  },
  {
    title: "Product Badge",
    href: "@apollo/storefront/ProductBadge",
    description: "Status badges for products (sale, new, featured)",
    keywords: ["badge", "status", "sale", "new", "featured", "label"],
    component: (
      <div
        style={{
          display: "flex",
          gap: "8px",
          flexDirection: "column",
        }}
      >
        <ProductBadge variant="discount" label="Sale" />
        <ProductBadge variant="lowStock" label="Low Stock" />
        <ProductBadge variant="monthly" label="Monthly Deal" />
        <ProductBadge variant="moreItem" label="+5 More" />
      </div>
    ),
  },
  {
    title: "Category Menu Item",
    href: "@apollo/storefront/CategoryMenuItem",
    description: "Navigation item for product categories with image and label",
    keywords: ["category", "menu", "item", "navigation", "image", "label"],
    component: (
      <CategoryMenuItem
        imageSrc="https://picsum.photos/60/60?random=12"
        imageAlt="Electronics"
        label="Electronics"
      />
    ),
  },
  {
    title: "Tab With Image",
    href: "@apollo/storefront/TabWithImage",
    description: "Tabbed navigation with image support for categories",
    keywords: ["tab", "navigation", "image", "category", "visual", "browsing"],
    component: (
      <div style={{ maxWidth: "400px" }}>
        <TabWithImage.Root defaultValue="electronics">
          <TabWithImage.List>
            <TabWithImage.Tab
              value="electronics"
              imageSrc="https://picsum.photos/60/60?random=10"
              imageAlt="Electronics"
              label="Electronics"
            />
            <TabWithImage.Tab
              value="clothing"
              imageSrc="https://picsum.photos/60/60?random=11"
              imageAlt="Clothing"
              label="Clothing"
            />
            <TabWithImage.Indicator />
          </TabWithImage.List>
          <TabWithImage.Panel value="electronics">
            <div
              style={{
                padding: "16px",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
              }}
            >
              Electronics content
            </div>
          </TabWithImage.Panel>
          <TabWithImage.Panel value="clothing">
            <div
              style={{
                padding: "16px",
                backgroundColor: "#f5f5f5",
                borderRadius: "4px",
              }}
            >
              Clothing content
            </div>
          </TabWithImage.Panel>
        </TabWithImage.Root>
      </div>
    ),
  },
]

export default storefrontComponents
