import { useCallback, useState } from "react"
import {
  ComponentRules,
  MultiplePropsTable,
  UsageGuidelines,
} from "@/components"
import { TabWithImage } from "@apollo/storefront"
import { Typography } from "@apollo/ui"
import {
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

/**
 * TabWithImage component
 *
 * The TabWithImage component provides a tabbed interface that supports images within tabs.
 * It's built on top of the base Tabs component from @apollo/ui and adds image support for
 * enhanced visual navigation. Perfect for product categories, visual navigation, and
 * content organization with rich media.
 *
 * Notes:
 * - Supports both images and SVG icons within tabs;
 * - Automatic text truncation with line-clamp for consistent layout;
 * - Horizontal scrolling support for mobile and overflow scenarios;
 * - Built with accessibility in mind using the underlying Tabs component.
 */
const meta = {
  title: "@apollo∕storefront/Components/Navigation/TabWithImage",
  component: TabWithImage.Root,
  subcomponents: {
    Tab: TabWithImage.Tab,
    List: TabWithImage.List,
    Panel: TabWithImage.Panel,
    Indicator: TabWithImage.Indicator,
  },
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "padded",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6072-4394",
    },
    docs: {
      description: {
        component:
          "The TabWithImage component provides a tabbed interface with image support for enhanced visual navigation. Built on top of the Apollo Tabs component with additional features for images, icons, and automatic text truncation.",
      },

      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { TabWithImage } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="tabwithimage-props">Props</h2>
          <MultiplePropsTable
            tabs={[
              {
                label: "TabWithImage.Root",
                props: [
                  {
                    name: "defaultValue",
                    description:
                      "The default selected tab value (uncontrolled mode).",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "value",
                    description:
                      "The currently selected tab value (controlled mode).",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "onValueChange",
                    description:
                      "Callback fired when the selected tab changes.",
                    type: "(value: string) => void",
                    defaultValue: "-",
                  },
                  {
                    name: "children",
                    description:
                      "Tab components and panels to be rendered within the tabs.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "TabWithImage.List",
                props: [
                  {
                    name: "children",
                    description:
                      "Tab components and indicator to be rendered within the list.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names.",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "TabWithImage.Tab",
                props: [
                  {
                    name: "value",
                    description: "The value of the tab.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "disabled",
                    description: "Whether the tab is disabled.",
                    defaultValue: "false",
                    type: "boolean",
                  },
                  {
                    name: "imageSrc",
                    description: "Image source URL.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "imageAlt",
                    description: "Alternative text for images.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "imageSrcSet",
                    description: "Image srcset for responsive images.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "icon",
                    description:
                      "SVG icon or other React element to display instead of image.",
                    type: "React.ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "label",
                    description:
                      "Text label for the tab (automatically truncated to 2 lines).",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names.",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },

              {
                label: "TabWithImage.Indicator",
                props: [
                  {
                    name: "className",
                    description: "Additional CSS class names.",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },
              {
                label: "TabWithImage.Panel",
                props: [
                  {
                    name: "value",
                    description: "The value that corresponds to the tab.",
                    type: "string",
                    defaultValue: "-",
                  },
                  {
                    name: "children",
                    description: "The content to display within the panel.",
                    type: "ReactNode",
                    defaultValue: "-",
                  },
                  {
                    name: "className",
                    description: "Additional CSS class names.",
                    type: "string",
                    defaultValue: "-",
                  },
                ],
              },
            ]}
          />
          <h2 id="tabwithimage-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use TabWithImage for: Visual navigation with product categories, image-based content organization, and enhanced user experience",
              "Keep tab labels short and descriptive, especially since text is automatically truncated to 2 lines",
              "Use high-quality, consistent image sizes (recommended: 80x80px) for better visual alignment",
              "Provide meaningful alt text for images using the imageAlt prop to ensure accessibility for screen readers",
              "Use SVG icons via the icon prop for scalable, crisp graphics that work well at different sizes",
              "Consider horizontal scrolling behavior on mobile devices when designing with many tabs",
              "Ensure sufficient color contrast between tab content and background for accessibility",
              "Use the label prop for text content which is automatically truncated to 2 lines for consistent styling",
            ]}
          />
          <h2 id="tabwithimage-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive <code>imageAlt</code> prop values for
                images to ensure their purpose is clear to screen reader users.
              </>,
              <>
                Use the <code>disabled</code> prop to disable tabs that are not
                currently actionable, ensuring they are not focusable.
              </>,
              <>
                Ensure tab labels are descriptive and meaningful using the{" "}
                <code>label</code> prop, as text will automatically truncate for
                visual consistency.
              </>,
              <>
                When using SVG icons via the <code>icon</code> prop, ensure they
                have appropriate <code>aria-label</code> or <code>title</code>{" "}
                attributes for screen reader accessibility.
              </>,
            ]}
          />

          <h2 id="tabwithimage-examples">Examples</h2>
          <Stories title="" />
          <h2 id="tabwithimage-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <TabWithImage.Root defaultValue="electronics">
                        <TabWithImage.List style={{ marginBottom: 16 }}>
                          <TabWithImage.Tab
                            value="electronics"
                            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
                            imageAlt="Electronics category"
                            label="Electronics"
                          />
                          <TabWithImage.Tab
                            value="fashion"
                            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
                            imageAlt="Fashion category"
                            label="Fashion"
                          />
                          <TabWithImage.Tab
                            value="home"
                            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
                            imageAlt="Home category"
                            label="Home & Garden"
                          />
                          <TabWithImage.Indicator />
                        </TabWithImage.List>
                        <TabWithImage.Panel value="electronics">
                          Electronics content
                        </TabWithImage.Panel>
                        <TabWithImage.Panel value="fashion">
                          Fashion content
                        </TabWithImage.Panel>
                        <TabWithImage.Panel value="home">
                          Home & Garden content
                        </TabWithImage.Panel>
                      </TabWithImage.Root>
                    </div>
                  ),
                  description:
                    "Use high-quality, consistent images with descriptive alt text and clear, concise labels",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <TabWithImage.Root defaultValue="tab1">
                        <TabWithImage.List style={{ marginBottom: 16 }}>
                          <TabWithImage.Tab
                            value="tab1"
                            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=40&h=60&fit=crop"
                            imageAlt=""
                            label="Tab 1 with very long text that will be truncated"
                          />
                          <TabWithImage.Tab
                            value="tab2"
                            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=100&h=40&fit=crop"
                            imageAlt=""
                            label="Tab 2"
                          />
                          <TabWithImage.Indicator />
                        </TabWithImage.List>
                        <TabWithImage.Panel value="tab1">
                          Content 1
                        </TabWithImage.Panel>
                        <TabWithImage.Panel value="tab2">
                          Content 2
                        </TabWithImage.Panel>
                      </TabWithImage.Root>
                    </div>
                  ),
                  description:
                    "Avoid inconsistent image sizes, missing alt text, and generic labels like 'Tab 1'",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ width: 300 }}>
                      <TabWithImage.Root defaultValue="favorites">
                        <TabWithImage.List style={{ marginBottom: 16 }}>
                          <TabWithImage.Tab
                            value="favorites"
                            icon={
                              <svg
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                              >
                                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                              </svg>
                            }
                            label="Favorites"
                          />
                          <TabWithImage.Tab
                            value="profile"
                            icon={
                              <svg
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                                fill="currentColor"
                              >
                                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                              </svg>
                            }
                            label="Profile"
                          />
                          <TabWithImage.Indicator />
                        </TabWithImage.List>
                        <TabWithImage.Panel value="favorites">
                          Favorites content
                        </TabWithImage.Panel>
                        <TabWithImage.Panel value="profile">
                          Profile content
                        </TabWithImage.Panel>
                      </TabWithImage.Root>
                    </div>
                  ),
                  description:
                    "Use consistent, well-designed SVG icons for scalable graphics",
                },
                negative: {
                  component: (
                    <div style={{ width: 300 }}>
                      <TabWithImage.Root defaultValue="mixed1">
                        <TabWithImage.List style={{ marginBottom: 16 }}>
                          <TabWithImage.Tab
                            value="mixed1"
                            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
                            imageAlt="Mixed"
                            label="Image"
                          />
                          <TabWithImage.Tab
                            value="mixed2"
                            icon={
                              <div
                                style={{
                                  width: 40,
                                  height: 40,
                                  background: "#ccc",
                                }}
                              >
                                ?
                              </div>
                            }
                            label="Icon"
                          />
                          <TabWithImage.Indicator />
                        </TabWithImage.List>
                        <TabWithImage.Panel value="mixed1">
                          Content 1
                        </TabWithImage.Panel>
                        <TabWithImage.Panel value="mixed2">
                          Content 2
                        </TabWithImage.Panel>
                      </TabWithImage.Root>
                    </div>
                  ),
                  description:
                    "Avoid mixing different visual styles (photos and placeholder graphics) within the same tab group",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    defaultValue: {
      control: { type: "text" },
      description: "The default selected tab value (uncontrolled mode).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    value: {
      control: { type: "text" },
      description: "The currently selected tab value (controlled mode).",
      table: {
        type: { summary: "string" },
      },
    },
    onValueChange: {
      control: false,
      description: "Callback fired when the selected tab changes.",
      table: {
        type: {
          summary: "(value: string) => void",
        },
      },
    },
    children: {
      control: false,
      description: "Tab components and panels to be rendered within the tabs.",
      table: {
        type: { summary: "ReactNode" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
      },
    },
  },
  args: {
    defaultValue: "tab1",
  },
} satisfies Meta<typeof TabWithImage.Root>

export default meta

type Story = StoryObj<typeof TabWithImage.Root>

/** Default TabWithImage (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview TabWithImage with default settings. Shows a horizontal group of tabs with images and text, with one tab pre-selected.",
      },
    },
  },
  render: (args) => (
    <TabWithImage.Root {...args}>
      <TabWithImage.List>
        <TabWithImage.Tab
          value="tab1"
          imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
          imageAlt="Electronics"
          label="Electronics"
        />
        <TabWithImage.Tab
          value="tab2"
          imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
          imageAlt="Fashion"
          label="Fashion"
        />
        <TabWithImage.Tab
          value="tab3"
          imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
          imageAlt="Home"
          label="Home & Garden"
        />
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="tab1">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Electronics Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Discover the latest in technology and electronics with great deals
            and quality products.
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="tab2">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Fashion Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Explore trendy clothing and accessories for all styles and
            occasions.
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="tab3">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Home & Garden Content</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Everything you need for your home and garden improvement projects.
          </Typography>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/** TabWithImage with different content types and variations */
export const Variants: Story = {
  parameters: {
    layout: "padded",
    docs: {
      description: {
        story:
          "A comprehensive showcase of TabWithImage variants including images, icons, text truncation, and disabled states.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr",
          gap: 32,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Images
          </Typography>
          <TabWithImage.Root defaultValue="electronics">
            <TabWithImage.List>
              <TabWithImage.Tab
                value="electronics"
                imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
                imageAlt="Electronics"
                label="Electronics"
              />
              <TabWithImage.Tab
                value="fashion"
                imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
                imageAlt="Fashion"
                label="Fashion"
              />
              <TabWithImage.Tab
                value="home"
                imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
                imageAlt="Home"
                label="Home & Garden"
              />
              <TabWithImage.Indicator />
            </TabWithImage.List>
            <TabWithImage.Panel value="electronics">
              <div style={{ padding: 16 }}>Electronics content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="fashion">
              <div style={{ padding: 16 }}>Fashion content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="home">
              <div style={{ padding: 16 }}>Home & Garden content</div>
            </TabWithImage.Panel>
          </TabWithImage.Root>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Icons
          </Typography>
          <TabWithImage.Root defaultValue="favorites">
            <TabWithImage.List>
              <TabWithImage.Tab
                value="favorites"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                  </svg>
                }
                label="Favorites"
              />
              <TabWithImage.Tab
                value="profile"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                  </svg>
                }
                label="Profile"
              />
              <TabWithImage.Tab
                value="settings"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
                  </svg>
                }
                label="Settings"
              />
              <TabWithImage.Indicator />
            </TabWithImage.List>
            <TabWithImage.Panel value="favorites">
              <div style={{ padding: 16 }}>Favorites content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="profile">
              <div style={{ padding: 16 }}>Profile content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="settings">
              <div style={{ padding: 16 }}>Settings content</div>
            </TabWithImage.Panel>
          </TabWithImage.Root>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            Full Width (Fill Content)
          </Typography>
          <TabWithImage.Root defaultValue="favorites">
            <TabWithImage.List>
              <TabWithImage.Tab
                value="favorites"
                variant="fill"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
                  </svg>
                }
                label="Favorites"
              />
              <TabWithImage.Tab
                value="profile"
                variant="fill"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                  </svg>
                }
                label="Profile"
              />
              <TabWithImage.Tab
                value="settings"
                variant="fill"
                icon={
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
                  </svg>
                }
                label="Settings"
              />
              <TabWithImage.Indicator />
            </TabWithImage.List>
            <TabWithImage.Panel value="favorites">
              <div style={{ padding: 16 }}>Favorites content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="profile">
              <div style={{ padding: 16 }}>Profile content</div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="settings">
              <div style={{ padding: 16 }}>Settings content</div>
            </TabWithImage.Panel>
          </TabWithImage.Root>
        </div>
      </div>
    )
  },
}

/** TabWithImage demonstrating text truncation behavior */
export const TextTruncation: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "TabWithImage demonstrating automatic text truncation for long labels. Text is automatically truncated to 2 lines for consistent visual layout.",
      },
    },
  },
  render: () => (
    <TabWithImage.Root defaultValue="long1">
      <TabWithImage.List>
        <TabWithImage.Tab
          value="long1"
          imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
          imageAlt="Electronics"
          label="Electronics and Modern Technology Products for Modern Living"
        />
        <TabWithImage.Tab
          value="long2"
          imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
          imageAlt="Fashion"
          label="Fashion Clothing and Accessories for All Ages at Reasonable Prices"
        />
        <TabWithImage.Tab
          value="long3"
          imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
          imageAlt="Home"
          label="Home and Garden Improvement Products and Supplies"
        />
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="long1">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Electronics</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            This tab demonstrates how long text labels are automatically
            truncated to maintain consistent layout across all tabs.
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="long2">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Fashion</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Long labels are truncated with ellipsis after 2 lines to ensure
            visual consistency.
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="long3">
        <div style={{ padding: 16 }}>
          <Typography level="bodyLarge">Home & Garden</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            The text truncation feature helps maintain a clean and organized
            appearance.
          </Typography>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/** TabWithImage with disabled state example */
export const Disabled: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "TabWithImage demonstrating disabled state functionality. Disabled tabs are not interactive and have reduced opacity to indicate their unavailable state.",
      },
    },
  },
  render: () => (
    <div style={{ display: "flex", flexDirection: "column", gap: 32 }}>
      <TabWithImage.Root defaultValue="available1">
        <TabWithImage.List>
          <TabWithImage.Tab
            value="available1"
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
            imageAlt="Available Electronics"
            label="Electronics"
          />
          <TabWithImage.Tab
            value="disabled1"
            disabled
            imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
            imageAlt="Disabled Fashion"
            label="Fashion (Coming Soon)"
          />
          <TabWithImage.Tab
            value="available2"
            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
            imageAlt="Available Home"
            label="Home & Garden"
          />
          <TabWithImage.Tab
            value="disabled2"
            disabled
            imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
            imageAlt="Disabled Sports"
            label="Sports (Unavailable)"
          />
          <TabWithImage.Indicator />
        </TabWithImage.List>
        <TabWithImage.Panel value="available1">
          <div style={{ padding: 16 }}>
            <Typography level="bodyLarge">Electronics Available</Typography>
            <Typography level="bodyMedium" style={{ marginTop: 8 }}>
              This tab is available and fully interactive. Users can click and
              navigate to this content.
            </Typography>
          </div>
        </TabWithImage.Panel>
        <TabWithImage.Panel value="disabled1">
          <div style={{ padding: 16 }}>
            <Typography level="bodyLarge">Fashion Coming Soon</Typography>
            <Typography level="bodyMedium" style={{ marginTop: 8 }}>
              This panel is not accessible because the tab is disabled.
            </Typography>
          </div>
        </TabWithImage.Panel>
        <TabWithImage.Panel value="available2">
          <div style={{ padding: 16 }}>
            <Typography level="bodyLarge">Home & Garden Available</Typography>
            <Typography level="bodyMedium" style={{ marginTop: 8 }}>
              Another available tab that users can interact with normally.
            </Typography>
          </div>
        </TabWithImage.Panel>
        <TabWithImage.Panel value="disabled2">
          <div style={{ padding: 16 }}>
            <Typography level="bodyLarge">Sports Unavailable</Typography>
            <Typography level="bodyMedium" style={{ marginTop: 8 }}>
              This panel is also not accessible due to the disabled state.
            </Typography>
          </div>
        </TabWithImage.Panel>
      </TabWithImage.Root>
      <TabWithImage.Root defaultValue="profile">
        <TabWithImage.List>
          <TabWithImage.Tab
            value="favorites"
            icon={
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" />
              </svg>
            }
            disabled
            label="Favorites"
          />
          <TabWithImage.Tab
            value="profile"
            icon={
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
              </svg>
            }
            label="Profile"
          />
          <TabWithImage.Tab
            value="settings"
            icon={
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="currentColor"
              >
                <path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23,0.41-0.12,0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z" />
              </svg>
            }
            label="Settings"
          />
          <TabWithImage.Indicator />
        </TabWithImage.List>
        <TabWithImage.Panel value="favorites">
          <div style={{ padding: 16 }}>Favorites content</div>
        </TabWithImage.Panel>
        <TabWithImage.Panel value="profile">
          <div style={{ padding: 16 }}>Profile content</div>
        </TabWithImage.Panel>
        <TabWithImage.Panel value="settings">
          <div style={{ padding: 16 }}>Settings content</div>
        </TabWithImage.Panel>
      </TabWithImage.Root>
    </div>
  ),
}

/** TabWithImage with controlled state example */
export const ControlledState: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Example of TabWithImage with controlled state management. The active tab is controlled by external state with callback handling.",
      },
    },
  },
  render: () => {
    function ControlledDemo() {
      const [selectedTab, setSelectedTab] = useState("electronics")

      const handleChange = useCallback((value: string) => {
        setSelectedTab(value)
      }, [])

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 16,
          }}
        >
          <TabWithImage.Root value={selectedTab} onValueChange={handleChange}>
            <TabWithImage.List>
              <TabWithImage.Tab
                value="electronics"
                imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
                imageAlt="Electronics"
                label="Electronics"
              />
              <TabWithImage.Tab
                value="fashion"
                imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
                imageAlt="Fashion"
                label="Fashion"
              />
              <TabWithImage.Tab
                value="home"
                imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
                imageAlt="Home"
                label="Home & Garden"
              />
              <TabWithImage.Indicator />
            </TabWithImage.List>
            <TabWithImage.Panel value="electronics">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">Electronics</Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Latest technology and electronic devices with warranty.
                </Typography>
              </div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="fashion">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">Fashion</Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Trendy clothing and accessories for all occasions.
                </Typography>
              </div>
            </TabWithImage.Panel>
            <TabWithImage.Panel value="home">
              <div style={{ padding: 16 }}>
                <Typography level="bodyLarge">Home & Garden</Typography>
                <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                  Everything you need for home improvement projects.
                </Typography>
              </div>
            </TabWithImage.Panel>
          </TabWithImage.Root>
          <Typography level="bodySmall" style={{ color: "#6b7280" }}>
            Selected: {selectedTab}
          </Typography>
        </div>
      )
    }
    return <ControlledDemo />
  },
}

/** TabWithImage with scrollable behavior for mobile */
export const ScrollableMobile: Story = {
  globals: {
    viewport: { value: "mobile2", isRotated: false },
  },
  parameters: {
    docs: {
      description: {
        story:
          "TabWithImage demonstrating horizontal scrolling behavior with many tabs. Perfect for mobile screens where horizontal scrolling is needed.",
      },
    },
  },
  render: () => (
    <TabWithImage.Root defaultValue="cat1">
      <TabWithImage.List>
        <TabWithImage.Tab
          value="cat1"
          imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
          imageAlt="Electronics"
          label="Promotions"
        />
        <TabWithImage.Tab
          value="cat2"
          imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
          imageAlt="Beverages"
          label="Beverages"
        />
        <TabWithImage.Tab
          value="cat3"
          imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
          imageAlt="Snacks"
          label="Snacks"
        />
        <TabWithImage.Tab
          value="cat4"
          imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=80&h=80&fit=crop"
          imageAlt="Health"
          label="Health Care"
        />
        <TabWithImage.Tab
          value="cat5"
          imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=80&h=80&fit=crop"
          imageAlt="Home"
          label="Home Goods"
        />
        <TabWithImage.Tab
          value="cat6"
          imageSrc="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=80&h=80&fit=crop"
          imageAlt="Pets"
          label="Pet Supplies"
        />
        <TabWithImage.Tab
          value="cat7"
          imageSrc="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=80&h=80&fit=crop"
          imageAlt="Electronics"
          label="Electronics"
        />
        <TabWithImage.Tab
          value="cat8"
          imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=80&h=80&fit=crop"
          imageAlt="Fashion"
          label="Fashion"
        />
        <TabWithImage.Indicator />
      </TabWithImage.List>
      <TabWithImage.Panel value="cat1">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Promotions</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Special discounts up to 50% off
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat2">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Beverages</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Fresh drinks and natural fruit juices
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat3">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Snacks</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Sweet and savory snacks in various flavors
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat4">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Health Care</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Dietary supplements and health equipment
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat5">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Home Goods</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Home appliances and household items
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat6">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Pet Supplies</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Food and equipment for pets
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat7">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Electronics</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Technology and electronic devices
          </Typography>
        </div>
      </TabWithImage.Panel>
      <TabWithImage.Panel value="cat8">
        <div style={{ padding: "20px" }}>
          <Typography level="bodyLarge">Fashion</Typography>
          <Typography level="bodyMedium" style={{ marginTop: 8 }}>
            Clothing and fashion accessories
          </Typography>
        </div>
      </TabWithImage.Panel>
    </TabWithImage.Root>
  ),
}

/** TabWithImage with custom content and rich layouts */
export const CustomContent: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "TabWithImage with custom content using rich layouts and complex structures. Demonstrates flexible panel designs with various content types.",
      },
    },
  },
  render: () => {
    function CustomContentDemo() {
      const [selectedTab, setSelectedTab] = useState("promotions")

      return (
        <div
          style={{
            display: "flex",
            flexDirection: "column",
            gap: 32,
          }}
        >
          <div>
            <Typography
              level="bodyLarge"
              style={{ fontWeight: "600", marginBottom: 16 }}
            >
              Product Categories Dashboard
            </Typography>
            <TabWithImage.Root
              value={selectedTab}
              onValueChange={setSelectedTab}
            >
              <TabWithImage.List style={{ marginBottom: 24 }}>
                <TabWithImage.Tab
                  value="promotions"
                  imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=80&h=80&fit=crop"
                  imageAlt="Promotions"
                  label="Promotions"
                />
                <TabWithImage.Tab
                  value="beverages"
                  imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=80&h=80&fit=crop"
                  imageAlt="Beverages"
                  label="Beverages"
                />
                <TabWithImage.Tab
                  value="snacks"
                  imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=80&h=80&fit=crop"
                  imageAlt="Snacks"
                  label="Snacks & Cakes"
                />
                <TabWithImage.Indicator />
              </TabWithImage.List>

              <TabWithImage.Panel value="promotions">
                <div
                  style={{
                    padding: 20,
                    backgroundColor: "#fff5f5",
                    borderRadius: 8,
                    border: "1px solid #fed7d7",
                  }}
                >
                  <Typography
                    level="bodyLarge"
                    style={{ fontWeight: "600", color: "#e53e3e" }}
                  >
                    🔥 Special Promotions
                  </Typography>
                  <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                    Up to 50% off on quality products and featured items
                  </Typography>
                  <div
                    style={{
                      display: "grid",
                      gridTemplateColumns:
                        "repeat(auto-fit, minmax(120px, 1fr))",
                      gap: 12,
                      marginTop: 16,
                    }}
                  >
                    <div style={{ textAlign: "center", padding: 8 }}>
                      <Typography
                        level="bodySmall"
                        style={{ fontWeight: "600" }}
                      >
                        Electronics
                      </Typography>
                      <Typography
                        level="bodySmall"
                        style={{ color: "#e53e3e" }}
                      >
                        -30%
                      </Typography>
                    </div>
                    <div style={{ textAlign: "center", padding: 8 }}>
                      <Typography
                        level="bodySmall"
                        style={{ fontWeight: "600" }}
                      >
                        Fashion
                      </Typography>
                      <Typography
                        level="bodySmall"
                        style={{ color: "#e53e3e" }}
                      >
                        -50%
                      </Typography>
                    </div>
                    <div style={{ textAlign: "center", padding: 8 }}>
                      <Typography
                        level="bodySmall"
                        style={{ fontWeight: "600" }}
                      >
                        Home
                      </Typography>
                      <Typography
                        level="bodySmall"
                        style={{ color: "#e53e3e" }}
                      >
                        -25%
                      </Typography>
                    </div>
                  </div>
                </div>
              </TabWithImage.Panel>

              <TabWithImage.Panel value="beverages">
                <div
                  style={{
                    padding: 20,
                    backgroundColor: "#f0f9ff",
                    borderRadius: 8,
                    border: "1px solid #bae6fd",
                  }}
                >
                  <Typography
                    level="bodyLarge"
                    style={{ fontWeight: "600", color: "#0369a1" }}
                  >
                    💧 Fresh Beverages
                  </Typography>
                  <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                    100% natural fruit juices with no preservatives
                  </Typography>
                  <ul style={{ marginTop: 12, paddingLeft: 20 }}>
                    <li>
                      <Typography level="bodySmall">
                        Orange Juice - Fresh squeezed daily
                      </Typography>
                    </li>
                    <li>
                      <Typography level="bodySmall">
                        Apple Juice - Organic apples
                      </Typography>
                    </li>
                    <li>
                      <Typography level="bodySmall">
                        Mixed Berry - Antioxidant rich
                      </Typography>
                    </li>
                    <li>
                      <Typography level="bodySmall">
                        Green Smoothies - Healthy blend
                      </Typography>
                    </li>
                  </ul>
                </div>
              </TabWithImage.Panel>

              <TabWithImage.Panel value="snacks">
                <div
                  style={{
                    padding: 20,
                    backgroundColor: "#fffbeb",
                    borderRadius: 8,
                    border: "1px solid #fed7aa",
                  }}
                >
                  <Typography
                    level="bodyLarge"
                    style={{ fontWeight: "600", color: "#ea580c" }}
                  >
                    🍰 Snacks & Cakes
                  </Typography>
                  <Typography level="bodyMedium" style={{ marginTop: 8 }}>
                    Delicious sweets and fresh cakes made daily with
                    high-quality ingredients
                  </Typography>
                  <div
                    style={{
                      display: "flex",
                      gap: 16,
                      marginTop: 16,
                      flexWrap: "wrap",
                    }}
                  >
                    <div style={{ flex: 1, minWidth: 120 }}>
                      <Typography
                        level="bodySmall"
                        style={{ fontWeight: "600" }}
                      >
                        Sweet Treats
                      </Typography>
                      <Typography level="bodySmall">
                        Cookies, muffins, pastries
                      </Typography>
                    </div>
                    <div style={{ flex: 1, minWidth: 120 }}>
                      <Typography
                        level="bodySmall"
                        style={{ fontWeight: "600" }}
                      >
                        Fresh Cakes
                      </Typography>
                      <Typography level="bodySmall">
                        Birthday, wedding, custom
                      </Typography>
                    </div>
                  </div>
                </div>
              </TabWithImage.Panel>
            </TabWithImage.Root>
          </div>
        </div>
      )
    }
    return <CustomContentDemo />
  },
}
